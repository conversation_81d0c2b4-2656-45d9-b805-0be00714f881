--- 项目配置与指令 ---

1.  **开发环境 (Development Environment):**
    * **操作系统 (Operating System):** Windows
    * **命令行终端 (Command-Line Terminal):** PowerShell
    * **关键指令 (Key Directives):**
        * **命令兼容性:** 所有提供的命令都必须与 PowerShell 兼容。例如，使用 `Copy-Item` 而不是 `cp`，使用 `Get-ChildItem` (或别名 `ls`, `dir`) 而不是 `ls -la`。
        * **命令连接:** 禁止使用 `&&` 连接命令。请将多个命令放在不同的行上，或者使用分号 `;` 分隔。推荐使用换行以确保脚本的清晰和可靠执行。

2.  **Python 环境 (Python Environment):**
    * **解释器路径 (Interpreter Path):** `.\.venv\Scripts\python.exe`
    * **关键指令 (Key Directives):** 所有与 Python 相关的操作（如运行脚本、安装包、启动应用）都必须明确使用此路径的解释器。
        * **示例 (Examples):**
            * 安装包: `.\.venv\Scripts\python.exe -m pip install <package_name>`
            * 运行脚本: `.\.venv\Scripts\python.exe your_script.py`

3.  **文档要求 (Documentation Requirements):**
    * **合并输出 (Consolidated Output):** 所有生成的说明文档、代码注释、配置指南等，都必须整合到 **一个单一的说明文档** 中。最终只应产出一份综合性文档，而不是多个零散的文件。
