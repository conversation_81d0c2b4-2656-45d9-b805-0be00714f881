#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试SNMP v3 UDP连通性检查功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ip_port_checker import <PERSON><PERSON><PERSON><PERSON>

def test_snmp_udp():
    """
    测试SNMP v3 UDP连通性检查
    """
    print("🧪 测试SNMP v3 UDP连通性检查功能")
    print("=" * 50)
    
    # 创建端口检查器
    checker = PortChecker(timeout=5)
    
    # 测试用例
    test_cases = [
        ("127.0.0.1", 161),  # 本地SNMP端口（如果有的话）
        ("*******", 161),    # Google DNS的SNMP端口（通常不会响应）
        ("***********", 161), # 常见路由器SNMP端口
    ]
    
    print("📋 测试用例:")
    for i, (ip, port) in enumerate(test_cases, 1):
        print(f"  {i}. {ip}:{port}")
    
    print("\n🚀 开始测试...")
    print("-" * 50)
    
    for i, (ip, port) in enumerate(test_cases, 1):
        print(f"\n🔍 测试 {i}: {ip}:{port}")
        try:
            result = checker.check_udp_port(ip, port)
            status = "✅ 连通" if result else "❌ 不连通"
            print(f"   结果: {status}")
        except Exception as e:
            print(f"   ❌ 测试异常: {str(e)}")
    
    print("\n" + "=" * 50)
    print("🎉 测试完成!")
    print("\n💡 说明:")
    print("  - 如果看到'snmpget命令不存在'的错误，说明需要安装SNMP工具")
    print("  - Windows: 可以下载Net-SNMP工具包")
    print("  - Linux: sudo apt-get install snmp-utils 或 sudo yum install net-snmp-utils")
    print("  - 大多数设备可能不会响应SNMP v3查询，这是正常的")

if __name__ == "__main__":
    test_snmp_udp()
